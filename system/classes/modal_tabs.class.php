<?php

/**
 * Modal Tab Management Class
 * 
 * Handles server-side tab management for the system-wide modal component.
 * Provides functionality for detecting modal requests, managing tab state,
 * and generating appropriate HTMX responses with OOB swapping.
 */
class modal_tabs {
    
    /**
     * Check if the current request is targeting the modal
     */
    public static function is_modal_request(): bool {
        return isset($_SERVER['HTTP_HX_TARGET']) && $_SERVER['HTTP_HX_TARGET'] === '#modal_body';
    }
    
    /**
     * Check if modal is currently open (has existing tabs)
     */
    public static function is_modal_open(): bool {
        // We can detect this by checking if there's an HX-Current-URL header
        // or by checking session state if we implement that
        return isset($_SERVER['HTTP_HX_CURRENT_URL']);
    }
    
    /**
     * Get tab title from request data
     */
    public static function get_tab_title($default = 'Content'): string {
        // Check for data-tab-title in the request
        if (isset($_POST['tab_title'])) {
            return $_POST['tab_title'];
        }

        // Check HX-Trigger-Name header for element that triggered the request
        if (isset($_SERVER['HTTP_HX_TRIGGER_NAME'])) {
            $trigger_name = $_SERVER['HTTP_HX_TRIGGER_NAME'];
            // Try to find data-tab-title from the triggering element
            // This would need to be passed as a hidden field or in hx-vals
        }

        // Check for common patterns in request data to generate titles
        if (isset($_POST['csn'])) {
            return 'Customer ' . $_POST['csn'];
        }

        if (isset($_POST['subscription_number'])) {
            return 'Subscription ' . $_POST['subscription_number'];
        }

        if (isset($_POST['quote_number'])) {
            return 'Quote ' . $_POST['quote_number'];
        }

        if (isset($_POST['user_id'])) {
            return 'User Details';
        }

        return $default;
    }
    
    /**
     * Generate a unique tab ID based on request data
     */
    public static function generate_tab_id(): string {
        $components = [];
        
        // Use request data to create a unique ID
        if (isset($_POST['csn'])) {
            $components[] = 'customer_' . $_POST['csn'];
        } elseif (isset($_POST['subscription_number'])) {
            $components[] = 'subscription_' . $_POST['subscription_number'];
        } elseif (isset($_POST['quote_number'])) {
            $components[] = 'quote_' . $_POST['quote_number'];
        } elseif (isset($_POST['user_id'])) {
            $components[] = 'user_' . $_POST['user_id'];
        } else {
            // Fallback to timestamp-based ID
            $components[] = 'tab_' . time() . '_' . rand(1000, 9999);
        }
        
        return implode('_', $components);
    }
    
    /**
     * Check if there are any pinned tabs (from session or other storage)
     */
    public static function has_pinned_tabs(): bool {
        // For now, we'll assume no pinned tabs initially
        // This could be enhanced to check session storage or database
        return false;
    }
    
    /**
     * Set appropriate HTMX response headers for tab management
     */
    public static function set_tab_headers($tab_id, $tab_title, $is_new_tab = true): void {
        if (self::is_modal_request()) {
            if ($is_new_tab) {
                // Trigger Alpine.js to add a new tab
                $tab_data = json_encode([
                    'id' => $tab_id,
                    'title' => $tab_title,
                    'active' => true,
                    'pinned' => false
                ]);
                
                header('HX-Trigger: {"addModalTab": ' . $tab_data . '}');
            } else {
                // Trigger Alpine.js to switch to existing tab
                header('HX-Trigger: {"switchModalTab": "' . $tab_id . '"}');
            }
        }
    }
    
    /**
     * Wrap content with tab management logic
     */
    public static function wrap_content($content, $tab_title = null): string {
        if (!self::is_modal_request()) {
            return $content;
        }
        
        $tab_id = self::generate_tab_id();
        $tab_title = $tab_title ?: self::get_tab_title();
        
        // Determine if this should be a new tab or replace existing
        $modal_open = self::is_modal_open();
        $has_pinned = self::has_pinned_tabs();
        $is_new_tab = $modal_open || $has_pinned;
        
        // Set appropriate headers
        self::set_tab_headers($tab_id, $tab_title, $is_new_tab);
        
        return $content;
    }
    
    /**
     * Simple helper to enable tab functionality for any modal content
     * Call this at the beginning of any API function that loads modal content
     */
    public static function enable_tabs($tab_title = null): void {
        if (!self::is_modal_request()) {
            return;
        }
        
        $tab_id = self::generate_tab_id();
        $tab_title = $tab_title ?: self::get_tab_title();
        
        // Determine if this should be a new tab or replace existing
        $modal_open = self::is_modal_open();
        $has_pinned = self::has_pinned_tabs();
        $is_new_tab = $modal_open || $has_pinned;
        
        // Set appropriate headers
        self::set_tab_headers($tab_id, $tab_title, $is_new_tab);
    }
}
