# Modal Tab Functionality

This document explains how to use the new tab functionality in the system-wide modal component.

## Overview

The modal now supports multiple tabs with the following features:
- **Automatic tab creation** when modal is already open
- **Pin functionality** to keep tabs persistent
- **Tab switching** with Alpine.js
- **Server-side tab management** with HTMX integration

## Tab Management Rules

1. **When modal is closed/not visible:** Opening content replaces the default tab (current behavior)
2. **When modal is already open:** Opening new content creates a new tab and switches to it
3. **Pin functionality:** Pinned tabs remain open and override rule 1 (always use rule 2 when any pinned tabs exist)

## Usage

### For API Functions

Add this line at the beginning of any API function that loads modal content:

```php
// Enable tab functionality with custom title
modal_tabs::enable_tabs('My Tab Title');

// Or let it auto-generate the title
modal_tabs::enable_tabs();
```

### For HTMX Buttons

Include the tab title in your `hx-vals`:

```php
'hx-vals' => json_encode([
    'your_data' => 'value',
    'tab_title' => 'Custom Tab Title'
])
```

Or use the `data-tab-title` attribute (legacy support):

```php
'data-tab-title' => 'Custom Tab Title'
```

## Examples

### Basic API Function
```php
function user_modal($p) {
    // Enable tabs with custom title
    modal_tabs::enable_tabs('User Details');
    
    // Your existing code...
    echo Edge::render('user-modal', ['user' => $user]);
}
```

### Button with Tab Title
```php
Edge::render('forms-button', [
    'hx-post' => APP_ROOT . '/api/customers/view',
    'hx-target' => '#modal_body',
    'hx-vals' => json_encode([
        'csn' => $customer_csn,
        'tab_title' => 'Customer ' . $customer_csn
    ]),
    '@click' => 'showModal = true'
]);
```

## Auto-Generated Titles

The system automatically generates tab titles based on common request parameters:

- `csn` → "Customer {csn}"
- `subscription_number` → "Subscription {number}"
- `quote_number` → "Quote {number}"
- `user_id` → "User Details"

## Technical Details

### Files Modified
- `system/components/edges/component-modal.edge.php` - Main modal component with tab UI
- `system/classes/modal_tabs.class.php` - Server-side tab management
- Various API files updated to use `modal_tabs::enable_tabs()`

### Alpine.js Data Structure
```javascript
{
    tabs: [
        {
            id: 'unique_tab_id',
            title: 'Tab Title',
            active: true,
            pinned: false
        }
    ],
    activeTab: 'unique_tab_id'
}
```

### HTMX Headers
The system uses custom HTMX triggers:
- `addModalTab` - Adds a new tab
- `switchModalTab` - Switches to existing tab

## Testing

Visit `/modal_test` to test the tab functionality with sample content.

## Migration

Existing modal content will continue to work without changes. To enable tab functionality:

1. Add `modal_tabs::enable_tabs()` to your API functions
2. Include `tab_title` in `hx-vals` for custom titles
3. Test the behavior with your specific use cases
